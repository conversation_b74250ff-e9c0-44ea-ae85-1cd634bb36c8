// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useCallback, useState} from 'react';
import {DeviceEventEmitter, StyleSheet} from 'react-native';

import {Events} from '@constants';
import {useIsTablet} from '@hooks/device';
import BottomSheet from '@screens/bottom_sheet';
import {getEmojiByName} from '@utils/emoji/helpers';

import Picker from './picker';
import PickerFooter from './picker/footer';

import type {AvailableScreens} from '@typings/screens/navigation';
import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

type Props = {
    componentId: AvailableScreens;
    onEmojiPress: (emoji: string) => void;
    imageUrl?: string;
    file?: ExtractedFileInfo;
    closeButtonId: string;
    isSelectingMultiple?: boolean | undefined;
    onClose?: (selectedEmojis: SelectedEmoji[]) => void;
};

const style = StyleSheet.create({
    contentStyle: {
        paddingTop: 14,
    },
});

const EmojiPickerScreen = ({closeButtonId, componentId, file, imageUrl, onEmojiPress, isSelectingMultiple = false, onClose}: Props) => {
    const isTablet = useIsTablet();
    const [selectedEmojis, setSelectedEmojis] = useState<SelectedEmoji[]>([]);
    const [cursorPosition, setCursorPosition] = useState(0);

    const handleEmojiPress = useCallback((emoji: string) => {
        if (isSelectingMultiple) {
            // For multiple selection, add to selected emojis instead of calling onEmojiPress immediately
            const emojiDataMew = getEmojiByName(emoji, []);
            let emojiCharacter = `:${emoji}:`;

            if (emojiDataMew?.image && emojiDataMew.category !== 'custom') {
                const codeArray: string[] = emojiDataMew.image.split('-');
                const code = codeArray.reduce((acc, c) => {
                    return acc + String.fromCodePoint(parseInt(c, 16));
                }, '');
                emojiCharacter = code;
            }

            const newEmoji: SelectedEmoji = {
                id: `${emoji}_${Date.now()}`,
                character: emojiCharacter,
                name: emoji,
            };

            setSelectedEmojis((prev) => [...prev, newEmoji]);
        } else {
            // For single selection, use original behavior
            onEmojiPress(emoji);
            DeviceEventEmitter.emit(Events.CLOSE_BOTTOM_SHEET);
        }
    }, [isSelectingMultiple, onEmojiPress]);

    const handleRemoveEmoji = useCallback((id: string) => {
        setSelectedEmojis((prev) => prev.filter((emoji) => emoji.id !== id));
    }, []);

    const handleRemoveEmojiAtPosition = useCallback((position: number) => {
        setSelectedEmojis((prev) => {
            const newEmojis = [...prev];
            if (position >= 0 && position < newEmojis.length) {
                // Calculate new cursor position after deletion
                let newCursorPosition = 0;
                for (let i = 0; i < position; i++) {
                    newCursorPosition += newEmojis[i].character.length;
                }

                newEmojis.splice(position, 1);

                // Update cursor position to be at the position where the emoji was deleted
                setCursorPosition(newCursorPosition);
            }
            return newEmojis;
        });
    }, []);

    const handleCursorPositionChange = useCallback((position: number) => {
        setCursorPosition(position);
    }, []);

    const handleRemoveAllEmojis = useCallback(() => {
        setSelectedEmojis([]);
        setCursorPosition(0);
    }, []);

    const handleDone = useCallback(() => {
        onClose?.(selectedEmojis);
        DeviceEventEmitter.emit(Events.CLOSE_BOTTOM_SHEET);
    }, [selectedEmojis, onClose]);

    const renderContent = useCallback(() => {
        return (
            <Picker
                onEmojiPress={handleEmojiPress}
                imageUrl={imageUrl}
                file={file}
                testID='emoji_picker'
                selectedEmojis={selectedEmojis}
                onRemoveEmoji={handleRemoveEmoji}
                onRemoveEmojiAtPosition={handleRemoveEmojiAtPosition}
                cursorPosition={cursorPosition}
                onCursorPositionChange={handleCursorPositionChange}
                onDone={handleDone}
                showPreview={isSelectingMultiple}
            />
        );
    }, [handleEmojiPress, imageUrl, file, selectedEmojis, handleRemoveEmoji, handleRemoveEmojiAtPosition, cursorPosition, handleCursorPositionChange, handleDone, isSelectingMultiple]);

    const renderFooter = useCallback((props: any) => {
        if (isTablet) {
            return undefined;
        }
        return (
            <PickerFooter
                selectedEmojis={selectedEmojis}
                cursorPosition={cursorPosition}
                onRemoveEmojiAtPosition={handleRemoveEmojiAtPosition}
                onRemoveAllEmojis={handleRemoveAllEmojis}
                {...props}
            />
        );
    }, [isTablet]);

    return (
        <BottomSheet
            renderContent={renderContent}
            closeButtonId={closeButtonId}
            componentId={componentId}
            contentStyle={style.contentStyle}
            initialSnapIndex={1}
            footerComponent={renderFooter}
            testID='post_options'
        />
    );
};

export default EmojiPickerScreen;
