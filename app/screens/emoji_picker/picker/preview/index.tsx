// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useRef, useEffect, useState } from "react";
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Platform,
    Animated,
    TextInput,
} from "react-native";





import { useTheme } from "@context/theme";
import { changeOpacity, makeStyleSheetFromTheme } from "@utils/theme";
import { typography } from "@utils/typography";

import type { SelectedEmoji } from "@components/post_draft/emoji_preview";

type Props = {
    selectedEmojis: SelectedEmoji[];
    onRemoveEmoji: (id: string) => void;
    onRemoveEmojiAtPosition?: (position: number) => void;
    onCursorPositionChange?: (position: number) => void;
    onDone: () => void;
    testID?: string;
};

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return StyleSheet.create({
        container: {
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
            borderRadius: 8,
            borderWidth: 1,
            borderColor: changeOpacity(theme.centerChannelColor, 0.16),
            paddingHorizontal: 8,
            paddingVertical: 5,
            maxHeight: 100,
            minHeight: 100,
            marginHorizontal: 8,
            marginVertical: 8,
        },
        header: {
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 12,
        },
        title: {
            color: theme.centerChannelColor,
            ...typography('Heading', 100, 'SemiBold'),
        },
        doneButton: {
            backgroundColor: theme.buttonBg,
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 8,
            minWidth: 64,
            height: 32,
            alignItems: "center",
            justifyContent: "center",
            ...Platform.select({
                ios: {
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 1 },
                    shadowOpacity: 0.2,
                    shadowRadius: 3,
                },
                android: {
                    elevation: 2,
                },
            }),
        },
        doneButtonText: {
            color: theme.buttonColor,
            ...typography('Body', 75, 'SemiBold'),
        },
        textInputContainer: {
            flex: 1,
            borderRadius: 8,
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.02),
        },
        textInput: {
            fontSize: 24,
            color: theme.centerChannelColor,
            paddingVertical: 8,
            paddingHorizontal: 8,
            minHeight: 50,
            textAlignVertical: 'top',
            lineHeight: 32,
        },
        emptyText: {
            color: changeOpacity(theme.centerChannelColor, 0.56),
            textAlign: "center",
            flex: 1,
            paddingVertical: 16,
            ...typography('Body', 75, 'Regular'),
        },
        emojiItem: {
            position: "relative",
            marginRight: 8,
            minWidth: 40,
            height: 40,
            flexShrink: 0,
            alignItems: "center",
            justifyContent: "center",
        },
        emojiCharacter: {
            fontSize: 24,
            textAlign: "center",
        },

    });
});

const EmojiPickerPreview = ({
    selectedEmojis,
    onRemoveEmoji,
    onRemoveEmojiAtPosition,
    onCursorPositionChange,
    onDone,
    testID = "emoji_picker_preview",
}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const textInputRef = useRef<TextInput>(null);

    // State for cursor position and text content
    const [cursorPosition, setCursorPosition] = useState(0);
    const [textValue, setTextValue] = useState('');

    // Helper function to convert cursor position to emoji index
    const getEmojiIndexFromCursorPosition = useCallback((cursor: number, emojis: SelectedEmoji[]) => {
        if (emojis.length === 0) return -1;

        let charCount = 0;
        for (let i = 0; i < emojis.length; i++) {
            const emojiLength = emojis[i].character.length;
            // If cursor is within this emoji's character range
            if (cursor <= charCount + emojiLength) {
                return i;
            }
            charCount += emojiLength;
        }

        // If cursor is at the end or beyond, return the last emoji index
        return emojis.length - 1;
    }, []);

    // Animation values
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.95)).current;

    // Update text value when emojis change
    useEffect(() => {
        const emojiText = selectedEmojis.map(emoji => emoji.character).join('');
        setTextValue(emojiText);
    }, [selectedEmojis]);

    // Update TextInput cursor position when cursor position changes externally
    useEffect(() => {
        if (textInputRef.current) {
            textInputRef.current.setNativeProps({
                selection: { start: cursorPosition, end: cursorPosition }
            });
        }
    }, [cursorPosition]);

    // Animate in when component mounts
    useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
                toValue: 1,
                tension: 100,
                friction: 8,
                useNativeDriver: true,
            }),
        ]).start();
    }, [fadeAnim, scaleAnim]);



    const handleDone = useCallback(() => {
        // Add a subtle press animation
        Animated.sequence([
            Animated.timing(scaleAnim, {
                toValue: 0.96,
                duration: 100,
                useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
                toValue: 1,
                duration: 100,
                useNativeDriver: true,
            }),
        ]).start(() => {
            onDone();
        });
    }, [onDone, scaleAnim]);

    // Handle text input changes (mainly for backspace/delete functionality)
    const handleTextChange = useCallback((text: string) => {
        // If text is shorter than current text, user pressed backspace
        if (text.length < textValue.length && onRemoveEmojiAtPosition) {
            let emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition, selectedEmojis);

            // For backspace behavior, if cursor is at the beginning of an emoji, delete the previous one
            if (cursorPosition > 0) {
                emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition - 1, selectedEmojis);
            }

            if (emojiIndex >= 0 && emojiIndex < selectedEmojis.length) {
                onRemoveEmojiAtPosition(emojiIndex);
            }
        }
    }, [textValue, cursorPosition, selectedEmojis, onRemoveEmojiAtPosition, getEmojiIndexFromCursorPosition]);

    // Handle cursor position changes
    const handleSelectionChange = useCallback((event: any) => {
        const { start } = event.nativeEvent.selection;
        setCursorPosition(start);
        onCursorPositionChange?.(start);
    }, [onCursorPositionChange]);













    if (selectedEmojis.length === 0) {
        return (
            <Animated.View
                style={[
                    styles.container,
                    {
                        opacity: fadeAnim,
                        transform: [{ scale: scaleAnim }]
                    }
                ]}
                testID={`${testID}.container`}
            >
                {/* <View style={styles.header}>
                    <Text style={styles.title}>Selected (0)</Text>
                </View>
                <Text style={styles.emptyText}>Tap emojis to select them</Text> */}
            </Animated.View>
        );
    }

    return (
        <Animated.View
            style={[
                styles.container,
                {
                    opacity: fadeAnim,
                    transform: [{ scale: scaleAnim }]
                }
            ]}
            testID={`${testID}.container`}
        >
            <View style={styles.header}>
                {/* <Text style={styles.title}>
                    Selected ({selectedEmojis.length})
                </Text> */}
                <TouchableOpacity
                    style={styles.doneButton}
                    onPress={handleDone}
                    testID={`${testID}.done_button`}
                    activeOpacity={0.8}
                    delayPressIn={0}
                    delayPressOut={50}
                >
                    <Text style={styles.doneButtonText}>تم</Text>
                </TouchableOpacity>

            </View>
            <View style={styles.textInputContainer}>
                <TextInput
                    ref={textInputRef}
                    style={styles.textInput}
                    value={textValue}
                    onChangeText={handleTextChange}
                    onSelectionChange={handleSelectionChange}
                    multiline={true}
                    editable={true}
                    showSoftInputOnFocus={false}
                    testID={`${testID}.text_input`}
                    selectionColor={theme.buttonBg}
                />
            </View>
        </Animated.View>
    );
};

export default EmojiPickerPreview;
