// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useCallback, useState} from 'react';
import {View, TouchableOpacity, Alert} from 'react-native';

import CompassIcon from '@components/compass_icon';
import {useTheme} from '@context/theme';
import {selectEmojiCategoryBarSection, useEmojiCategoryBar} from '@hooks/emoji_category_bar';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';

import EmojiCategoryBarIcon from './icon';

import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        justifyContent: 'space-between',
        backgroundColor: theme.centerChannelBg,
        height: 55,
        paddingHorizontal: 12,
        paddingTop: 11,
        borderTopColor: changeOpacity(theme.centerChannelColor, 0.08),
        borderTopWidth: 1,
        flexDirection: 'row',
    },
    backspaceButton: {
        width: 32,
        height: 32,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 8,
        backgroundColor: changeOpacity(theme.errorTextColor, 0.1),
        borderRadius: 6,
        borderWidth: 1,
        borderColor: changeOpacity(theme.errorTextColor, 0.2),
    },
    backspaceIcon: {
        color: theme.errorTextColor,
    },
    backspaceButtonHidden: {
        opacity: 0
    },
    backspaceButtonVisible: {
        opacity: 1,
    },
}));

type Props = {
    onSelect?: (index: number | undefined) => void;
    selectedEmojis?: SelectedEmoji[];
    cursorPosition?: number;
    onRemoveEmojiAtPosition?: (position: number) => void;
    onRemoveAllEmojis?: () => void;
}

const EmojiCategoryBar = ({onSelect, selectedEmojis = [], cursorPosition = 0, onRemoveEmojiAtPosition, onRemoveAllEmojis}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const {currentIndex, icons} = useEmojiCategoryBar();

    // Helper function to convert cursor position to emoji index
    const getEmojiIndexFromCursorPosition = useCallback((cursor: number, emojis: SelectedEmoji[]) => {
        if (emojis.length === 0) return -1;

        let charCount = 0;
        for (let i = 0; i < emojis.length; i++) {
            const emojiLength = emojis[i].character.length;
            // If cursor is within this emoji's character range
            if (cursor <= charCount + emojiLength) {
                return i;
            }
            charCount += emojiLength;
        }

        // If cursor is at the end or beyond, return the last emoji index
        return emojis.length - 1;
    }, []);

    const scrollToIndex = useCallback((index: number) => {
        if (onSelect) {
            onSelect(index);
            return;
        }

        selectEmojiCategoryBarSection(index);
    }, []);

    // Handle single emoji deletion at cursor position
    const handleBackspacePress = useCallback(() => {
        if (selectedEmojis.length > 0 && onRemoveEmojiAtPosition) {
            let emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition, selectedEmojis);

            // For backspace behavior, if cursor is at the beginning of an emoji, delete the previous one
            if (cursorPosition > 0) {
                emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition - 1, selectedEmojis);
            }

            if (emojiIndex >= 0 && emojiIndex < selectedEmojis.length) {
                onRemoveEmojiAtPosition(emojiIndex);
            }
        }
    }, [cursorPosition, selectedEmojis, onRemoveEmojiAtPosition, getEmojiIndexFromCursorPosition]);

    // Handle multiple emoji deletion (long press)
    const handleBackspaceLongPress = useCallback(() => {
        if (selectedEmojis.length > 0 && onRemoveAllEmojis) {
            Alert.alert(
                'Clear All Emojis',
                `Are you sure you want to remove all ${selectedEmojis.length} emojis?`,
                [
                    {
                        text: 'Cancel',
                        style: 'cancel',
                    },
                    {
                        text: 'Clear All',
                        style: 'destructive',
                        onPress: () => {
                            onRemoveAllEmojis();
                        },
                    },
                ]
            );
        }
    }, [selectedEmojis, onRemoveAllEmojis]);

    if (!icons) {
        return null;
    }

    return (
        <View
            style={styles.container}
            testID='emoji_picker.category_bar'
        >
            {/* Backspace button as first icon */}
            <TouchableOpacity
                style={[
                    styles.backspaceButton,
                    selectedEmojis.length === 0 ? styles.backspaceButtonHidden : styles.backspaceButtonVisible
                ]}
                onPress={selectedEmojis.length > 0 ? handleBackspacePress : undefined}
                onLongPress={selectedEmojis.length > 0 ? handleBackspaceLongPress : undefined}
                delayLongPress={500}
                testID="emoji_picker.category_bar.backspace_button"
                activeOpacity={selectedEmojis.length > 0 ? 0.7 : 1}
                accessibilityLabel="Delete emoji at cursor position"
                accessibilityRole="button"
                accessibilityHint="Tap to delete emoji at cursor position, long press to delete all emojis"
                disabled={selectedEmojis.length === 0}
            >
                <CompassIcon
                    name="trash-can-outline"
                    size={18}
                    style={styles.backspaceIcon}
                />
            </TouchableOpacity>

            {/* Category icons */}
            {icons.map((icon, index) => (
                <EmojiCategoryBarIcon
                    currentIndex={currentIndex}
                    key={icon.key}
                    icon={icon.icon}
                    index={index}
                    scrollToIndex={scrollToIndex}
                    theme={theme}
                />
            ))}
        </View>
    );
};

export default EmojiCategoryBar;
