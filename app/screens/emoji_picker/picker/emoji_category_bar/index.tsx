// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useCallback, useState} from 'react';
import {View, TouchableOpacity, Alert} from 'react-native';

import CompassIcon from '@components/compass_icon';
import {useTheme} from '@context/theme';
import {selectEmojiCategoryBarSection, useEmojiCategoryBar} from '@hooks/emoji_category_bar';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';

import EmojiCategoryBarIcon from './icon';

import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        justifyContent: 'space-between',
        backgroundColor: theme.centerChannelBg,
        height: 55,
        paddingHorizontal: 12,
        paddingTop: 11,
        borderTopColor: changeOpacity(theme.centerChannelColor, 0.08),
        borderTopWidth: 1,
        flexDirection: 'row',
    },
    backspaceButton: {
        width: 32,
        height: 32,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: -6,
        right: 9,
        backgroundColor: changeOpacity(theme.errorTextColor, 0.1),
        borderRadius: 6,
        borderWidth: 1,
        borderColor: changeOpacity(theme.errorTextColor, 0.2),
    },
    backspaceIcon: {
        color: theme.errorTextColor,
    },
    backspaceButtonHidden: {
        opacity: 1
    },
    backspaceButtonVisible: {
        opacity: 0,
    },
}));

type Props = {
    onSelect?: (index: number | undefined) => void;
    selectedEmojis?: SelectedEmoji[];
    cursorPosition?: number;
    onRemoveEmojiAtPosition?: (position: number) => void;
    onRemoveAllEmojis?: () => void;
}

const EmojiCategoryBar = ({onSelect, selectedEmojis = [], cursorPosition = 0, onRemoveEmojiAtPosition, onRemoveAllEmojis}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const {currentIndex, icons} = useEmojiCategoryBar();

    // Log component props on every render
    console.log('🔧 EmojiCategoryBar render:', {
        selectedEmojisCount: selectedEmojis.length,
        cursorPosition,
        hasOnRemoveEmojiAtPosition: !!onRemoveEmojiAtPosition,
        hasOnRemoveAllEmojis: !!onRemoveAllEmojis,
        selectedEmojis: selectedEmojis.map(e => ({ id: e.id, character: e.character }))
    });

    // Helper function to convert cursor position to emoji index
    const getEmojiIndexFromCursorPosition = useCallback((cursor: number, emojis: SelectedEmoji[]) => {
        console.log('🔍 getEmojiIndexFromCursorPosition called:', { cursor, emojisCount: emojis.length });

        if (emojis.length === 0) {
            console.log('🔍 No emojis, returning -1');
            return -1;
        }

        let charCount = 0;
        for (let i = 0; i < emojis.length; i++) {
            const emojiLength = emojis[i].character.length;
            console.log(`🔍 Emoji ${i}: "${emojis[i].character}" (length: ${emojiLength}), charCount: ${charCount}`);

            // If cursor is within this emoji's character range
            if (cursor <= charCount + emojiLength) {
                console.log(`🔍 Cursor ${cursor} is within emoji ${i} range, returning index ${i}`);
                return i;
            }
            charCount += emojiLength;
        }

        // If cursor is at the end or beyond, return the last emoji index
        const lastIndex = emojis.length - 1;
        console.log(`🔍 Cursor ${cursor} is at end, returning last index ${lastIndex}`);
        return lastIndex;
    }, []);

    const scrollToIndex = useCallback((index: number) => {
        if (onSelect) {
            onSelect(index);
            return;
        }

        selectEmojiCategoryBarSection(index);
    }, []);

    // Handle single emoji deletion at cursor position
    const handleBackspacePress = useCallback(() => {
        console.log('🚀 handleBackspacePress CALLED!');
        console.log('🚀 Current state:', {
            selectedEmojisCount: selectedEmojis.length,
            cursorPosition,
            hasOnRemoveEmojiAtPosition: !!onRemoveEmojiAtPosition,
            onRemoveEmojiAtPositionType: typeof onRemoveEmojiAtPosition
        });

        if (selectedEmojis.length > 0 && onRemoveEmojiAtPosition) {
            console.log('🚀 Conditions met, proceeding with deletion');

            let emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition, selectedEmojis);
            console.log('🚀 Initial emoji index:', emojiIndex);

            // For backspace behavior, if cursor is at the beginning of an emoji, delete the previous one
            if (cursorPosition > 0) {
                console.log('🚀 Cursor > 0, adjusting index for backspace behavior');
                emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition - 1, selectedEmojis);
                console.log('🚀 Adjusted emoji index:', emojiIndex);
            }

            if (emojiIndex >= 0 && emojiIndex < selectedEmojis.length) {
                console.log('🚀 Calling onRemoveEmojiAtPosition with index:', emojiIndex);
                console.log('🚀 Emoji to be deleted:', selectedEmojis[emojiIndex]);
                onRemoveEmojiAtPosition(emojiIndex);
                console.log('🚀 onRemoveEmojiAtPosition called successfully');
            } else {
                console.log('🚀 Invalid emoji index:', emojiIndex, 'selectedEmojis.length:', selectedEmojis.length);
            }
        } else {
            console.log('🚀 Conditions NOT met:', {
                hasEmojis: selectedEmojis.length > 0,
                hasCallback: !!onRemoveEmojiAtPosition
            });
        }
    }, [cursorPosition, selectedEmojis, onRemoveEmojiAtPosition, getEmojiIndexFromCursorPosition]);

    // Handle multiple emoji deletion (long press)
    const handleBackspaceLongPress = useCallback(() => {
        console.log('🔥 handleBackspaceLongPress CALLED!');
        console.log('🔥 Current state:', {
            selectedEmojisCount: selectedEmojis.length,
            hasOnRemoveAllEmojis: !!onRemoveAllEmojis,
            onRemoveAllEmojisType: typeof onRemoveAllEmojis
        });

        if (selectedEmojis.length > 0 && onRemoveAllEmojis) {
            console.log('🔥 Conditions met, showing confirmation dialog');
            Alert.alert(
                'Clear All Emojis',
                `Are you sure you want to remove all ${selectedEmojis.length} emojis?`,
                [
                    {
                        text: 'Cancel',
                        style: 'cancel',
                        onPress: () => {
                            console.log('🔥 User cancelled clear all');
                        }
                    },
                    {
                        text: 'Clear All',
                        style: 'destructive',
                        onPress: () => {
                            console.log('🔥 User confirmed clear all, calling onRemoveAllEmojis');
                            onRemoveAllEmojis();
                            console.log('🔥 onRemoveAllEmojis called successfully');
                        },
                    },
                ]
            );
        } else {
            console.log('🔥 Conditions NOT met:', {
                hasEmojis: selectedEmojis.length > 0,
                hasCallback: !!onRemoveAllEmojis
            });
        }
    }, [selectedEmojis, onRemoveAllEmojis]);

    // Don't return null if we have selected emojis - we still want to show the backspace button
    if (!icons && selectedEmojis.length === 0) {
        console.log('🔧 No icons available and no selected emojis, returning null');
        return null;
    }

    if (!icons) {
        console.log('🔧 No icons available but we have selected emojis, rendering backspace button only');
    }

    // Log button state before rendering
    const isDisabled = selectedEmojis.length === 0;
    console.log('🔧 Button render state:', {
        isVisible: selectedEmojis.length > 0,
        isDisabled,
        buttonOpacity: selectedEmojis.length === 0 ? 'hidden (1)' : 'visible (0)',
        hasOnPress: !!handleBackspacePress,
        hasOnLongPress: !!handleBackspaceLongPress,
        iconsAvailable: !!icons,
        iconsCount: icons?.length || 0
    });

    return (
        <View
            style={styles.container}
            testID='emoji_picker.category_bar'
        >
            {/* Backspace button as first icon */}
            <TouchableOpacity
                style={[
                    styles.backspaceButton,
                    selectedEmojis.length === 0 ? styles.backspaceButtonHidden : styles.backspaceButtonVisible
                ]}
                onPress={() => {
                    console.log('👆 TouchableOpacity onPress triggered!');
                    console.log('👆 selectedEmojis.length:', selectedEmojis.length);
                    if (selectedEmojis.length > 0) {
                        console.log('👆 Calling handleBackspacePress');
                        handleBackspacePress();
                    } else {
                        console.log('👆 No emojis selected, not calling handler');
                    }
                }}
                onLongPress={() => {
                    console.log('👆 TouchableOpacity onLongPress triggered!');
                    console.log('👆 selectedEmojis.length:', selectedEmojis.length);
                    if (selectedEmojis.length > 0) {
                        console.log('👆 Calling handleBackspaceLongPress');
                        handleBackspaceLongPress();
                    } else {
                        console.log('👆 No emojis selected, not calling handler');
                    }
                }}
                delayLongPress={500}
                testID="emoji_picker.category_bar.backspace_button"
                activeOpacity={selectedEmojis.length > 0 ? 0.7 : 1}
                accessibilityLabel="Delete emoji at cursor position"
                accessibilityRole="button"
                accessibilityHint="Tap to delete emoji at cursor position, long press to delete all emojis"
                disabled={selectedEmojis.length === 0}
            >
                <CompassIcon
                    name="trash-can-outline"
                    size={18}
                    style={styles.backspaceIcon}
                />
            </TouchableOpacity>

            {/* Category icons */}
            {icons?.map((icon, index) => (
                <EmojiCategoryBarIcon
                    currentIndex={currentIndex}
                    key={icon.key}
                    icon={icon.icon}
                    index={index}
                    scrollToIndex={scrollToIndex}
                    theme={theme}
                />
            ))}
        </View>
    );
};

export default EmojiCategoryBar;
