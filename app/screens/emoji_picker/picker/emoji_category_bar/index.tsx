// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useCallback, useState} from 'react';
import {View, TouchableOpacity, Alert} from 'react-native';

import CompassIcon from '@components/compass_icon';
import {useTheme} from '@context/theme';
import {selectEmojiCategoryBarSection, useEmojiCategoryBar} from '@hooks/emoji_category_bar';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';

import EmojiCategoryBarIcon from './icon';

import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        justifyContent: 'space-between',
        backgroundColor: theme.centerChannelBg,
        height: 55,
        paddingHorizontal: 12,
        paddingTop: 11,
        borderTopColor: changeOpacity(theme.centerChannelColor, 0.08),
        borderTopWidth: 1,
        flexDirection: 'row',
    },
    backspaceButton: {
        width: 36,
        height: 36,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 8,
        backgroundColor: changeOpacity(theme.errorTextColor, 0.15),
        borderRadius: 8,
        borderWidth: 1.5,
        borderColor: changeOpacity(theme.errorTextColor, 0.3),
        shadowColor: theme.centerChannelColor,
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    backspaceIcon: {
        color: theme.errorTextColor,
    },
    backspaceButtonHidden: {
        opacity: 0,
        pointerEvents: 'none',
    },
    backspaceButtonVisible: {
        opacity: 1,
        pointerEvents: 'auto',
    },
}));

type Props = {
    onSelect?: (index: number | undefined) => void;
    selectedEmojis?: SelectedEmoji[];
    cursorPosition?: number;
    onRemoveEmojiAtPosition?: (position: number) => void;
    onRemoveAllEmojis?: () => void;
}

const EmojiCategoryBar = ({onSelect, selectedEmojis = [], cursorPosition = 0, onRemoveEmojiAtPosition, onRemoveAllEmojis}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const {currentIndex, icons} = useEmojiCategoryBar();

    // Log component props on every render
    console.log('🔧 EmojiCategoryBar render:', {
        selectedEmojisCount: selectedEmojis.length,
        cursorPosition,
        hasOnRemoveEmojiAtPosition: !!onRemoveEmojiAtPosition,
        hasOnRemoveAllEmojis: !!onRemoveAllEmojis,
        selectedEmojis: selectedEmojis.map(e => ({ id: e.id, character: e.character }))
    });

    // Helper function to convert cursor position to emoji index
    const getEmojiIndexFromCursorPosition = useCallback((cursor: number, emojis: SelectedEmoji[]) => {
        console.log('🔍 getEmojiIndexFromCursorPosition called:', { cursor, emojisCount: emojis.length });

        if (emojis.length === 0) {
            console.log('🔍 No emojis, returning -1');
            return -1;
        }

        let charCount = 0;
        for (let i = 0; i < emojis.length; i++) {
            const emojiLength = emojis[i].character.length;
            console.log(`🔍 Emoji ${i}: "${emojis[i].character}" (length: ${emojiLength}), charCount: ${charCount}`);

            // If cursor is within this emoji's character range
            if (cursor <= charCount + emojiLength) {
                console.log(`🔍 Cursor ${cursor} is within emoji ${i} range, returning index ${i}`);
                return i;
            }
            charCount += emojiLength;
        }

        // If cursor is at the end or beyond, return the last emoji index
        const lastIndex = emojis.length - 1;
        console.log(`🔍 Cursor ${cursor} is at end, returning last index ${lastIndex}`);
        return lastIndex;
    }, []);

    const scrollToIndex = useCallback((index: number) => {
        if (onSelect) {
            onSelect(index);
            return;
        }

        selectEmojiCategoryBarSection(index);
    }, []);

    // Handle single emoji deletion at cursor position
    const handleBackspacePress = useCallback(() => {
        console.log('🚀 handleBackspacePress CALLED!');
        console.log('🚀 Current state:', {
            selectedEmojisCount: selectedEmojis.length,
            cursorPosition,
            hasOnRemoveEmojiAtPosition: !!onRemoveEmojiAtPosition,
            onRemoveEmojiAtPositionType: typeof onRemoveEmojiAtPosition
        });

        if (selectedEmojis.length > 0 && onRemoveEmojiAtPosition) {
            console.log('🚀 Conditions met, proceeding with deletion');

            let emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition, selectedEmojis);
            console.log('🚀 Initial emoji index:', emojiIndex);

            // For backspace behavior, if cursor is at the beginning of an emoji, delete the previous one
            if (cursorPosition > 0) {
                console.log('🚀 Cursor > 0, adjusting index for backspace behavior');
                emojiIndex = getEmojiIndexFromCursorPosition(cursorPosition - 1, selectedEmojis);
                console.log('🚀 Adjusted emoji index:', emojiIndex);
            }

            if (emojiIndex >= 0 && emojiIndex < selectedEmojis.length) {
                console.log('🚀 Calling onRemoveEmojiAtPosition with index:', emojiIndex);
                console.log('🚀 Emoji to be deleted:', selectedEmojis[emojiIndex]);
                onRemoveEmojiAtPosition(emojiIndex);
                console.log('🚀 onRemoveEmojiAtPosition called successfully');
            } else {
                console.log('🚀 Invalid emoji index:', emojiIndex, 'selectedEmojis.length:', selectedEmojis.length);
            }
        } else {
            console.log('🚀 Conditions NOT met:', {
                hasEmojis: selectedEmojis.length > 0,
                hasCallback: !!onRemoveEmojiAtPosition
            });
        }
    }, [cursorPosition, selectedEmojis, onRemoveEmojiAtPosition, getEmojiIndexFromCursorPosition]);

    // Handle multiple emoji deletion (long press)
    const handleBackspaceLongPress = useCallback(() => {
        console.log('🔥 handleBackspaceLongPress CALLED!');
        console.log('🔥 Current state:', {
            selectedEmojisCount: selectedEmojis.length,
            hasOnRemoveAllEmojis: !!onRemoveAllEmojis,
            onRemoveAllEmojisType: typeof onRemoveAllEmojis
        });

        if (selectedEmojis.length > 0 && onRemoveAllEmojis) {
            console.log('🔥 Conditions met, showing confirmation dialog');
            Alert.alert(
                'Clear All Emojis',
                `Are you sure you want to remove all ${selectedEmojis.length} emojis?`,
                [
                    {
                        text: 'Cancel',
                        style: 'cancel',
                        onPress: () => {
                            console.log('🔥 User cancelled clear all');
                        }
                    },
                    {
                        text: 'Clear All',
                        style: 'destructive',
                        onPress: () => {
                            console.log('🔥 User confirmed clear all, calling onRemoveAllEmojis');
                            onRemoveAllEmojis();
                            console.log('🔥 onRemoveAllEmojis called successfully');
                        },
                    },
                ]
            );
        } else {
            console.log('🔥 Conditions NOT met:', {
                hasEmojis: selectedEmojis.length > 0,
                hasCallback: !!onRemoveAllEmojis
            });
        }
    }, [selectedEmojis, onRemoveAllEmojis]);

    // Always render the component if we have selected emojis (for backspace button)
    // Only return null if no icons AND no selected emojis
    const shouldRender = icons || selectedEmojis.length > 0;

    if (!shouldRender) {
        console.log('🔧 No icons available and no selected emojis, returning null');
        return null;
    }

    if (!icons && selectedEmojis.length > 0) {
        console.log('🔧 No icons available but we have selected emojis, rendering backspace button only');
    }

    // Determine button visibility and state
    const hasSelectedEmojis = selectedEmojis.length > 0;
    const isButtonVisible = hasSelectedEmojis;
    const isButtonDisabled = !hasSelectedEmojis;

    console.log('🔧 Button render state:', {
        hasSelectedEmojis,
        isButtonVisible,
        isButtonDisabled,
        selectedEmojisCount: selectedEmojis.length,
        buttonOpacity: isButtonVisible ? 'visible (1)' : 'hidden (0)',
        hasOnPress: !!handleBackspacePress,
        hasOnLongPress: !!handleBackspaceLongPress,
        iconsAvailable: !!icons,
        iconsCount: icons?.length || 0
    });

    return (
        <View
            style={styles.container}
            testID='emoji_picker.category_bar'
        >
            {/* Backspace button as first icon */}
            <TouchableOpacity
                style={[
                    styles.backspaceButton,
                    isButtonVisible ? styles.backspaceButtonVisible : styles.backspaceButtonHidden,
                    isButtonVisible && {
                        backgroundColor: changeOpacity(theme.errorTextColor, 0.2),
                        borderColor: changeOpacity(theme.errorTextColor, 0.4),
                    },
                    !isButtonVisible && {
                        backgroundColor: changeOpacity(theme.centerChannelColor, 0.1),
                        borderColor: changeOpacity(theme.centerChannelColor, 0.2),
                    }
                ]}
                hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                onPress={() => {
                    console.log('👆 TouchableOpacity onPress triggered!');
                    console.log('👆 hasSelectedEmojis:', hasSelectedEmojis, 'count:', selectedEmojis.length);
                    if (hasSelectedEmojis) {
                        console.log('👆 Calling handleBackspacePress');
                        handleBackspacePress();
                    } else {
                        console.log('👆 No emojis selected, not calling handler');
                    }
                }}
                onLongPress={() => {
                    console.log('👆 TouchableOpacity onLongPress triggered!');
                    console.log('👆 hasSelectedEmojis:', hasSelectedEmojis, 'count:', selectedEmojis.length);
                    if (hasSelectedEmojis) {
                        console.log('👆 Calling handleBackspaceLongPress');
                        handleBackspaceLongPress();
                    } else {
                        console.log('👆 No emojis selected, not calling handler');
                    }
                }}
                delayLongPress={500}
                testID="emoji_picker.category_bar.backspace_button"
                activeOpacity={isButtonVisible ? 0.6 : 1}
                accessibilityLabel={isButtonVisible ? "Delete emoji at cursor position" : "No emojis to delete"}
                accessibilityRole="button"
                accessibilityHint={isButtonVisible ? "Tap to delete emoji at cursor position, long press to delete all emojis" : "Select emojis first"}
                disabled={isButtonDisabled}
            >
                <CompassIcon
                    name="trash-can-outline"
                    size={20}
                    style={[
                        styles.backspaceIcon,
                        !isButtonVisible && { opacity: 0.3 }
                    ]}
                />
            </TouchableOpacity>

            {/* Category icons */}
            {icons?.map((icon, index) => (
                <EmojiCategoryBarIcon
                    currentIndex={currentIndex}
                    key={icon.key}
                    icon={icon.icon}
                    index={index}
                    scrollToIndex={scrollToIndex}
                    theme={theme}
                />
            ))}
        </View>
    );
};

export default EmojiCategoryBar;
