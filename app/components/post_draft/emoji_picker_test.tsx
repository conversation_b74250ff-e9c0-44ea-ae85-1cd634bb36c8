// Test component to verify emoji picker functionality
// This is a temporary test file to validate the new emoji selection workflow

import React, { useState } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet } from 'react-native';
import { openAsBottomSheet } from '@screens/navigation';
import { Screens } from '@constants';
import { useTheme } from '@context/theme';
import { getEmojiByName } from '@utils/emoji/helpers';
import type { SelectedEmoji } from './emoji_preview';

const EmojiPickerTest = () => {
    const theme = useTheme();
    const [text, setText] = useState('Hello world! ');
    const [cursorPosition, setCursorPosition] = useState(text.length);

    const handleEmojiPickerOpen = () => {
        openAsBottomSheet({
            closeButtonId: 'close-emoji-test',
            screen: Screens.EMOJI_PICKER,
            theme,
            title: "Select Emojis",
            props: {
                onEmojiPress: (emoji: string) => {
                    // For single emoji selection, insert directly
                    const emojiDataMew = getEmojiByName(emoji, []);
                    let emojiCharacter = `:${emoji}:`;

                    if (emojiDataMew?.image && emojiDataMew.category !== 'custom') {
                        const codeArray: string[] = emojiDataMew.image.split('-');
                        const code = codeArray.reduce((acc, c) => {
                            return acc + String.fromCodePoint(parseInt(c, 16));
                        }, '');
                        emojiCharacter = code;
                    }

                    // Insert emoji at cursor position
                    const beforeCursor = text.substring(0, cursorPosition);
                    const afterCursor = text.substring(cursorPosition);
                    const newText = beforeCursor + emojiCharacter + afterCursor;
                    
                    setText(newText);
                    setCursorPosition(cursorPosition + emojiCharacter.length);
                },
                isSelectingMultiple: true,
                onClose: (selectedEmojis: SelectedEmoji[]) => {
                    // Insert all selected emojis at cursor position when picker closes
                    if (selectedEmojis.length > 0) {
                        const emojiText = selectedEmojis.map(emoji => emoji.character).join(' ');
                        const beforeCursor = text.substring(0, cursorPosition);
                        const afterCursor = text.substring(cursorPosition);
                        const newText = beforeCursor + emojiText + afterCursor;
                        
                        setText(newText);
                        setCursorPosition(cursorPosition + emojiText.length);
                    }
                }
            },
        });
    };

    const styles = StyleSheet.create({
        container: {
            padding: 20,
            backgroundColor: theme.centerChannelBg,
        },
        title: {
            fontSize: 18,
            fontWeight: 'bold',
            color: theme.centerChannelColor,
            marginBottom: 20,
        },
        textInput: {
            borderWidth: 1,
            borderColor: theme.centerChannelColor,
            borderRadius: 8,
            padding: 12,
            fontSize: 16,
            color: theme.centerChannelColor,
            backgroundColor: theme.centerChannelBg,
            minHeight: 100,
            textAlignVertical: 'top',
            marginBottom: 20,
        },
        button: {
            backgroundColor: theme.buttonBg,
            padding: 12,
            borderRadius: 8,
            alignItems: 'center',
        },
        buttonText: {
            color: theme.buttonColor,
            fontSize: 16,
            fontWeight: '600',
        },
        info: {
            fontSize: 14,
            color: theme.centerChannelColor,
            marginBottom: 10,
            opacity: 0.7,
        },
    });

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Emoji Picker Test</Text>
            <Text style={styles.info}>
                Test the new emoji selection workflow:
                {'\n'}1. Tap "Open Emoji Picker"
                {'\n'}2. Select multiple emojis (they should appear in preview at TOP of picker, below search bar)
                {'\n'}3. Test backspace functionality:
                {'\n'}   - Single tap backspace: deletes emoji at cursor position
                {'\n'}   - Long press backspace: shows confirmation to delete all emojis
                {'\n'}4. Tap "Done" to insert them into the text input
                {'\n'}5. Emojis should be inserted at cursor position
                {'\n'}6. Preview should have smooth horizontal scrolling when many emojis are selected
            </Text>
            <TextInput
                style={styles.textInput}
                value={text}
                onChangeText={setText}
                onSelectionChange={(event) => {
                    setCursorPosition(event.nativeEvent.selection.start);
                }}
                multiline
                placeholder="Type here and test emoji insertion..."
                placeholderTextColor={theme.centerChannelColor + '80'}
            />
            <TouchableOpacity style={styles.button} onPress={handleEmojiPickerOpen}>
                <Text style={styles.buttonText}>Open Emoji Picker</Text>
            </TouchableOpacity>
            <TouchableOpacity
                style={[styles.button, { backgroundColor: theme.errorTextColor }]}
                onPress={() => {
                    console.log('🧪 TEST: Clearing console logs for fresh test');
                    console.clear?.();
                    console.log('🧪 TEST: Console cleared, ready for emoji picker testing');
                }}
            >
                <Text style={styles.buttonText}>Clear Console & Start Test</Text>
            </TouchableOpacity>
        </View>
    );
};

export default EmojiPickerTest;
